# 指纹浏览器 (Fingerprint Browser)

一个能够生成不同浏览器指纹的工具，每次启动都会创建具有独特指纹的浏览器实例，用于隐私保护和测试目的。

## 🌟 特性

- **随机指纹生成**: 每次启动生成不同的浏览器指纹
- **多种指纹参数**: User-Agent、屏幕分辨率、时区、语言、WebGL等
- **配置文件隔离**: 每个浏览器实例使用独立的配置文件
- **多实例支持**: 可同时启动多个不同指纹的浏览器
- **临时/永久配置**: 支持临时配置和永久配置文件
- **跨平台支持**: 支持 Windows、macOS、Linux
- **交互式界面**: 提供友好的命令行交互界面

## 📋 系统要求

- Python 3.7+
- Chrome 或 Chromium 浏览器
- psutil 库

## 🚀 安装

1. 克隆或下载项目文件到本地目录
2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 确保系统已安装 Chrome 或 Chromium 浏览器

## 💻 使用方法

### 交互模式（推荐）

启动交互式界面：
```bash
python main.py -i
```

交互模式提供以下功能：
- 启动单个/多个浏览器
- 查看运行中的浏览器状态
- 终止浏览器进程
- 生成和预览指纹
- 管理配置文件

### 命令行模式

#### 启动单个浏览器
```bash
# 使用默认设置启动
python main.py

# 指定访问的URL
python main.py --url https://www.example.com

# 使用临时配置
python main.py --temp --url https://www.google.com

# 使用指定配置文件名
python main.py --profile my_profile --url https://www.google.com
```

#### 启动多个浏览器
```bash
# 启动3个浏览器实例
python main.py --count 3 --url https://www.google.com
```

### 命令行参数

- `--url`: 要访问的URL（默认：https://www.google.com）
- `--count`: 启动的浏览器数量（默认：1）
- `--interactive, -i`: 启动交互模式
- `--profile`: 使用指定的配置文件名称
- `--temp`: 使用临时配置文件

## 🔧 指纹参数

系统会随机生成以下指纹参数：

### 基础参数
- **User-Agent**: 随机选择常见的浏览器标识
- **屏幕分辨率**: 常见的显示器分辨率
- **颜色深度**: 24位或32位
- **像素比**: 1.0, 1.25, 1.5, 2.0等

### 系统参数
- **时区**: 全球主要时区
- **语言**: 多种语言和地区设置
- **平台**: Windows、macOS、Linux

### 硬件参数
- **CPU核心数**: 2-16核
- **设备内存**: 2-32GB
- **WebGL厂商**: NVIDIA、Intel、AMD等

### 其他参数
- **Canvas指纹**: 随机Canvas渲染特征
- **Do Not Track**: 随机开启/关闭
- **Java支持**: 随机开启/关闭

## 📁 项目结构

```
fingerweb/
├── main.py                 # 主程序入口
├── fingerprint_generator.py # 指纹生成器
├── browser_config.py       # 浏览器配置管理
├── browser_launcher.py     # 浏览器启动器
├── requirements.txt        # 依赖列表
├── README.md              # 说明文档
└── browser_configs/       # 配置文件目录（自动创建）
```

## 🛡️ 隐私说明

本工具旨在：
- 保护用户隐私，防止浏览器指纹追踪
- 提供测试环境，用于开发和测试
- 研究浏览器指纹技术

请遵守当地法律法规，不要用于非法用途。

## ⚠️ 注意事项

1. **浏览器要求**: 需要安装 Chrome 或 Chromium 浏览器
2. **资源占用**: 多个浏览器实例会占用较多系统资源
3. **配置清理**: 临时配置会在程序退出时自动清理
4. **网络环境**: 某些网站可能对频繁的不同指纹访问有限制

## 🔍 故障排除

### 常见问题

**Q: 提示"未找到Chrome/Chromium浏览器"**
A: 请确保已安装 Chrome 或 Chromium，并且安装在标准路径

**Q: 浏览器启动失败**
A: 检查是否有足够的系统资源，尝试减少同时启动的实例数量

**Q: 配置文件占用空间过大**
A: 定期清理不需要的永久配置文件，或使用临时配置模式

**Q: 进程无法正常退出**
A: 使用 Ctrl+C 强制退出，或在交互模式中选择终止所有浏览器

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基础指纹生成和浏览器启动
- 提供交互式命令行界面
- 支持配置文件管理

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
