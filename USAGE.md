# 指纹浏览器使用指南

## 🚀 快速开始

### Windows 用户
1. 双击运行 `run.bat` 文件
2. 或在命令行中执行：
   ```cmd
   python main.py -i
   ```

### Linux/macOS 用户
1. 在终端中执行：
   ```bash
   chmod +x run.sh
   ./run.sh
   ```
2. 或直接运行：
   ```bash
   python3 main.py -i
   ```

## 📖 详细使用说明

### 交互模式功能

启动交互模式后，您将看到以下选项：

#### 1. 启动单个浏览器
- 输入要访问的URL（默认：Google）
- 选择使用临时配置或永久配置
- 如果选择永久配置，需要输入配置文件名称

#### 2. 启动多个浏览器
- 输入要启动的浏览器数量（1-10个）
- 输入要访问的URL
- 系统会为每个浏览器生成不同的指纹

#### 3. 查看运行中的浏览器
- 显示所有正在运行的浏览器实例
- 包含PID、内存使用率、CPU使用率等信息
- 显示每个实例的User-Agent信息

#### 4. 终止浏览器
- 选择要终止的特定浏览器实例
- 或一次性终止所有浏览器实例

#### 5. 生成指纹预览
- 预览随机生成的浏览器指纹
- 可以保存指纹到JSON文件

#### 6. 管理配置文件
- 查看所有保存的配置文件
- 删除不需要的配置文件
- 查看配置文件的详细指纹信息

## 🔧 命令行参数详解

### 基本用法
```bash
python main.py [选项]
```

### 可用选项

| 参数 | 说明 | 示例 |
|------|------|------|
| `--url` | 指定访问的URL | `--url https://www.example.com` |
| `--count` | 启动的浏览器数量 | `--count 3` |
| `--interactive, -i` | 启动交互模式 | `-i` |
| `--profile` | 使用指定配置文件 | `--profile my_profile` |
| `--temp` | 使用临时配置 | `--temp` |

### 使用示例

#### 启动单个浏览器访问特定网站
```bash
python main.py --url https://www.github.com
```

#### 启动3个浏览器实例
```bash
python main.py --count 3 --url https://www.google.com
```

#### 使用指定配置文件
```bash
python main.py --profile test_profile --url https://www.example.com
```

#### 使用临时配置
```bash
python main.py --temp --url https://www.baidu.com
```

## 🎯 使用场景

### 1. 隐私保护
- 防止网站通过浏览器指纹追踪用户
- 每次访问使用不同的指纹信息

### 2. 网站测试
- 测试网站在不同浏览器环境下的表现
- 模拟不同地区、不同设备的用户访问

### 3. 爬虫开发
- 为网络爬虫提供不同的浏览器指纹
- 降低被反爬虫系统检测的风险

### 4. 安全研究
- 研究浏览器指纹技术
- 测试指纹识别系统的准确性

## ⚡ 性能优化建议

### 系统资源
- 每个浏览器实例大约占用 200-500MB 内存
- 建议同时运行的实例数不超过系统内存的合理范围
- 在低配置设备上建议限制在 2-3 个实例

### 启动优化
- 使用临时配置可以减少磁盘占用
- 定期清理不需要的永久配置文件
- 避免在短时间内启动过多实例

## 🛠️ 故障排除

### 常见错误及解决方案

#### 1. "未找到Chrome/Chromium浏览器"
**原因**: 系统中没有安装Chrome或Chromium浏览器
**解决**: 
- 下载并安装 [Google Chrome](https://www.google.com/chrome/)
- 或安装 [Chromium](https://www.chromium.org/)

#### 2. "ModuleNotFoundError: No module named 'psutil'"
**原因**: 缺少必要的Python依赖
**解决**: 
```bash
pip install -r requirements.txt
```

#### 3. 浏览器启动失败
**可能原因**:
- 系统资源不足
- Chrome浏览器被其他程序占用
- 权限不足

**解决方案**:
- 关闭其他不必要的程序释放内存
- 重启系统
- 以管理员权限运行程序

#### 4. 配置文件权限错误
**原因**: 没有写入权限
**解决**: 
- 确保程序有写入当前目录的权限
- 或将程序移动到有写入权限的目录

### 调试模式

如果遇到问题，可以修改代码启用调试模式：

1. 编辑 `browser_launcher.py`
2. 将 `stdout=subprocess.DEVNULL` 改为 `stdout=None`
3. 将 `stderr=subprocess.DEVNULL` 改为 `stderr=None`

这样可以看到Chrome的详细错误信息。

## 📝 配置文件说明

### config.json 配置项

- `browser.default_url`: 默认访问的URL
- `browser.max_instances`: 最大实例数限制
- `browser.startup_delay`: 启动延迟（秒）
- `fingerprint.randomize_on_startup`: 是否在启动时随机化指纹
- `profiles.base_config_dir`: 配置文件存储目录
- `chrome.additional_args`: 额外的Chrome启动参数

### 自定义配置

您可以编辑 `config.json` 文件来自定义默认行为：

```json
{
  "browser": {
    "default_url": "https://your-website.com",
    "max_instances": 5
  }
}
```

## 🔒 安全注意事项

1. **合法使用**: 请确保在合法范围内使用本工具
2. **网站条款**: 遵守目标网站的使用条款和robots.txt
3. **频率控制**: 避免过于频繁的请求，以免对服务器造成压力
4. **数据保护**: 不要在浏览器中输入敏感信息，因为配置可能被清理

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. 首先查看本使用指南和README.md
2. 检查是否满足系统要求
3. 尝试重新安装依赖包
4. 查看错误信息并对照故障排除部分

记住：合理使用工具，遵守法律法规！
