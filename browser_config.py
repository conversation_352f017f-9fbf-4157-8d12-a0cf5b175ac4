#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器 - 浏览器配置管理模块
管理浏览器配置文件，实现配置隔离和指纹应用
"""

import os
import json
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Optional
import uuid


class BrowserConfigManager:
    """浏览器配置管理器"""
    
    def __init__(self, base_config_dir: str = "browser_configs"):
        self.base_config_dir = Path(base_config_dir)
        self.base_config_dir.mkdir(exist_ok=True)
        self.temp_configs = []  # 临时配置目录列表
    
    def create_profile(self, fingerprint: Dict, profile_name: Optional[str] = None) -> str:
        """创建新的浏览器配置文件"""
        if profile_name is None:
            profile_name = f"profile_{uuid.uuid4().hex[:8]}"
        
        profile_dir = self.base_config_dir / profile_name
        profile_dir.mkdir(exist_ok=True)
        
        # 创建Chrome配置目录结构
        self._create_chrome_profile_structure(profile_dir)
        
        # 应用指纹配置
        self._apply_fingerprint_to_profile(profile_dir, fingerprint)
        
        return str(profile_dir)
    
    def create_temp_profile(self, fingerprint: Dict) -> str:
        """创建临时浏览器配置文件"""
        temp_dir = tempfile.mkdtemp(prefix="browser_profile_")
        self.temp_configs.append(temp_dir)
        
        # 创建Chrome配置目录结构
        self._create_chrome_profile_structure(Path(temp_dir))
        
        # 应用指纹配置
        self._apply_fingerprint_to_profile(Path(temp_dir), fingerprint)
        
        return temp_dir
    
    def _create_chrome_profile_structure(self, profile_dir: Path):
        """创建Chrome配置目录结构"""
        # 创建必要的子目录
        subdirs = [
            "Default",
            "Default/Extensions",
            "Default/Local Storage",
            "Default/Session Storage",
            "Default/IndexedDB",
            "Default/Cache",
            "Default/Code Cache",
            "ShaderCache",
            "GrShaderCache"
        ]
        
        for subdir in subdirs:
            (profile_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    def _apply_fingerprint_to_profile(self, profile_dir: Path, fingerprint: Dict):
        """将指纹配置应用到浏览器配置文件"""
        # 创建Preferences文件
        preferences = self._generate_preferences(fingerprint)
        preferences_file = profile_dir / "Default" / "Preferences"
        
        with open(preferences_file, 'w', encoding='utf-8') as f:
            json.dump(preferences, f, indent=2)
        
        # 创建Local State文件
        local_state = self._generate_local_state(fingerprint)
        local_state_file = profile_dir / "Local State"
        
        with open(local_state_file, 'w', encoding='utf-8') as f:
            json.dump(local_state, f, indent=2)
        
        # 保存指纹信息
        fingerprint_file = profile_dir / "fingerprint.json"
        with open(fingerprint_file, 'w', encoding='utf-8') as f:
            json.dump(fingerprint, f, indent=2, ensure_ascii=False)
    
    def _generate_preferences(self, fingerprint: Dict) -> Dict:
        """生成Chrome Preferences配置"""
        preferences = {
            "profile": {
                "default_content_setting_values": {
                    "geolocation": 1,
                    "media_stream_camera": 1,
                    "media_stream_mic": 1,
                    "notifications": 1
                },
                "content_settings": {
                    "exceptions": {}
                }
            },
            "intl": {
                "accept_languages": fingerprint["language"].split(',')[0].split(';')[0]
            },
            "webkit": {
                "webprefs": {
                    "default_encoding": "UTF-8"
                }
            },
            "browser": {
                "window_placement": {
                    "bottom": fingerprint["screen"]["height"],
                    "left": 0,
                    "maximized": False,
                    "right": fingerprint["screen"]["width"],
                    "top": 0,
                    "work_area_bottom": fingerprint["screen"]["height"],
                    "work_area_left": 0,
                    "work_area_right": fingerprint["screen"]["width"],
                    "work_area_top": 0
                }
            },
            "extensions": {
                "settings": {}
            }
        }
        
        return preferences
    
    def _generate_local_state(self, fingerprint: Dict) -> Dict:
        """生成Chrome Local State配置"""
        local_state = {
            "browser": {
                "enabled_labs_experiments": []
            },
            "user_experience_metrics": {
                "stability": {
                    "stats_version": 5
                }
            },
            "variations_seed_version": "20231201-120000.123456"
        }
        
        return local_state
    
    def get_chrome_launch_args(self, profile_dir: str, fingerprint: Dict) -> List[str]:
        """生成Chrome启动参数"""
        args = [
            f"--user-data-dir={profile_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            f"--window-size={fingerprint['screen']['width']},{fingerprint['screen']['height']}",
            f"--user-agent={fingerprint['user_agent']}",
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-extensions-http-throttling",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-report-upload",
            "--hide-scrollbars",
            "--mute-audio"
        ]
        
        # 添加语言设置
        lang = fingerprint["language"].split(',')[0].split(';')[0]
        args.append(f"--lang={lang}")
        
        # 添加时区设置
        args.append(f"--timezone={fingerprint['timezone']}")
        
        return args
    
    def cleanup_temp_profiles(self):
        """清理临时配置文件"""
        for temp_dir in self.temp_configs:
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                print(f"清理临时配置失败: {temp_dir}, 错误: {e}")
        
        self.temp_configs.clear()
    
    def list_profiles(self) -> List[str]:
        """列出所有配置文件"""
        if not self.base_config_dir.exists():
            return []
        
        profiles = []
        for item in self.base_config_dir.iterdir():
            if item.is_dir():
                profiles.append(item.name)
        
        return profiles
    
    def delete_profile(self, profile_name: str) -> bool:
        """删除指定的配置文件"""
        profile_dir = self.base_config_dir / profile_name
        if profile_dir.exists():
            try:
                shutil.rmtree(profile_dir)
                return True
            except Exception as e:
                print(f"删除配置文件失败: {e}")
                return False
        return False
    
    def get_profile_fingerprint(self, profile_name: str) -> Optional[Dict]:
        """获取配置文件的指纹信息"""
        fingerprint_file = self.base_config_dir / profile_name / "fingerprint.json"
        if fingerprint_file.exists():
            try:
                with open(fingerprint_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"读取指纹文件失败: {e}")
        return None


if __name__ == "__main__":
    # 测试配置管理器
    from fingerprint_generator import FingerprintGenerator
    
    generator = FingerprintGenerator()
    fingerprint = generator.generate_fingerprint()
    
    config_manager = BrowserConfigManager()
    profile_dir = config_manager.create_temp_profile(fingerprint)
    
    print(f"创建临时配置文件: {profile_dir}")
    print("Chrome启动参数:")
    args = config_manager.get_chrome_launch_args(profile_dir, fingerprint)
    for arg in args:
        print(f"  {arg}")
