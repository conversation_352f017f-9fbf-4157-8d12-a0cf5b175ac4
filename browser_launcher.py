#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器 - 浏览器启动器模块
启动配置好指纹的浏览器实例
"""

import os
import sys
import subprocess
import platform
import time
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import atexit

from fingerprint_generator import FingerprintGenerator
from browser_config import BrowserConfigManager


class BrowserLauncher:
    """浏览器启动器"""
    
    def __init__(self):
        self.config_manager = BrowserConfigManager()
        self.fingerprint_generator = FingerprintGenerator()
        self.running_processes = []  # 运行中的浏览器进程
        
        # 注册退出时清理函数
        atexit.register(self.cleanup_all)
    
    def find_chrome_executable(self) -> Optional[str]:
        """查找Chrome/Chromium可执行文件"""
        system = platform.system().lower()
        
        if system == "windows":
            possible_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
                r"C:\Program Files\Chromium\Application\chromium.exe",
                r"C:\Program Files (x86)\Chromium\Application\chromium.exe"
            ]
        elif system == "darwin":  # macOS
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser"
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium",
                "/usr/bin/google-chrome-unstable",
                "/usr/bin/google-chrome-beta"
            ]
        
        for path in possible_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                return path
        
        return None
    
    def launch_browser(self, 
                      fingerprint: Optional[Dict] = None, 
                      profile_name: Optional[str] = None,
                      url: str = "about:blank",
                      temp_profile: bool = True) -> Tuple[subprocess.Popen, str]:
        """启动浏览器实例"""
        
        # 查找Chrome可执行文件
        chrome_path = self.find_chrome_executable()
        if not chrome_path:
            raise RuntimeError("未找到Chrome/Chromium浏览器，请确保已安装Chrome或Chromium")
        
        # 生成或使用指纹
        if fingerprint is None:
            fingerprint = self.fingerprint_generator.generate_fingerprint()
        
        # 创建配置文件
        if temp_profile:
            profile_dir = self.config_manager.create_temp_profile(fingerprint)
        else:
            profile_dir = self.config_manager.create_profile(fingerprint, profile_name)
        
        # 获取启动参数
        launch_args = self.config_manager.get_chrome_launch_args(profile_dir, fingerprint)
        
        # 构建完整的命令
        cmd = [chrome_path] + launch_args + [url]
        
        print(f"启动浏览器...")
        print(f"配置文件目录: {profile_dir}")
        print(f"User-Agent: {fingerprint['user_agent']}")
        print(f"屏幕分辨率: {fingerprint['screen']['width']}x{fingerprint['screen']['height']}")
        print(f"时区: {fingerprint['timezone']}")
        print(f"语言: {fingerprint['language']}")
        
        try:
            # 启动浏览器进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if platform.system() == "Windows" else 0
            )
            
            # 等待一下确保进程启动
            time.sleep(2)
            
            if process.poll() is None:  # 进程仍在运行
                self.running_processes.append((process, profile_dir, temp_profile))
                print(f"浏览器启动成功! PID: {process.pid}")
                return process, profile_dir
            else:
                raise RuntimeError("浏览器启动失败")
                
        except Exception as e:
            # 清理配置文件
            if temp_profile:
                self.config_manager.cleanup_temp_profiles()
            raise RuntimeError(f"启动浏览器失败: {e}")
    
    def launch_multiple_browsers(self, count: int, url: str = "about:blank") -> List[Tuple[subprocess.Popen, str]]:
        """启动多个浏览器实例"""
        browsers = []
        
        for i in range(count):
            try:
                print(f"\n启动第 {i+1} 个浏览器实例...")
                process, profile_dir = self.launch_browser(url=url, temp_profile=True)
                browsers.append((process, profile_dir))
                
                # 稍微延迟一下，避免同时启动太多进程
                time.sleep(1)
                
            except Exception as e:
                print(f"启动第 {i+1} 个浏览器失败: {e}")
        
        return browsers
    
    def get_running_browsers(self) -> List[Dict]:
        """获取运行中的浏览器信息"""
        running = []
        
        # 清理已结束的进程
        self.running_processes = [(p, d, t) for p, d, t in self.running_processes if p.poll() is None]
        
        for process, profile_dir, temp_profile in self.running_processes:
            try:
                # 获取进程信息
                proc_info = psutil.Process(process.pid)
                
                # 获取指纹信息
                fingerprint = None
                fingerprint_file = Path(profile_dir) / "fingerprint.json"
                if fingerprint_file.exists():
                    fingerprint = self.fingerprint_generator.load_fingerprint(str(fingerprint_file))
                
                running.append({
                    "pid": process.pid,
                    "profile_dir": profile_dir,
                    "temp_profile": temp_profile,
                    "memory_percent": proc_info.memory_percent(),
                    "cpu_percent": proc_info.cpu_percent(),
                    "fingerprint": fingerprint
                })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return running
    
    def kill_browser(self, pid: int) -> bool:
        """终止指定PID的浏览器进程"""
        try:
            # 查找对应的进程
            for i, (process, profile_dir, temp_profile) in enumerate(self.running_processes):
                if process.pid == pid:
                    # 终止进程
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()  # 强制终止
                    
                    # 清理临时配置
                    if temp_profile:
                        try:
                            import shutil
                            shutil.rmtree(profile_dir)
                        except Exception as e:
                            print(f"清理临时配置失败: {e}")
                    
                    # 从列表中移除
                    self.running_processes.pop(i)
                    return True
            
            return False
            
        except Exception as e:
            print(f"终止浏览器进程失败: {e}")
            return False
    
    def kill_all_browsers(self):
        """终止所有浏览器进程"""
        for process, profile_dir, temp_profile in self.running_processes[:]:
            try:
                self.kill_browser(process.pid)
            except Exception as e:
                print(f"终止进程 {process.pid} 失败: {e}")
    
    def cleanup_all(self):
        """清理所有资源"""
        print("\n正在清理资源...")
        self.kill_all_browsers()
        self.config_manager.cleanup_temp_profiles()


if __name__ == "__main__":
    # 测试浏览器启动器
    launcher = BrowserLauncher()
    
    try:
        # 启动一个浏览器实例
        process, profile_dir = launcher.launch_browser(url="https://www.google.com")
        
        print(f"\n浏览器已启动，按 Enter 键退出...")
        input()
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        launcher.cleanup_all()
