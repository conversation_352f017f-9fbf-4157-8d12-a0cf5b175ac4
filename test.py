#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器测试脚本
"""

import json
import sys

def test_fingerprint_generator():
    """测试指纹生成器"""
    print("测试指纹生成器...")
    try:
        from fingerprint_generator import FingerprintGenerator
        
        generator = FingerprintGenerator()
        fingerprint = generator.generate_fingerprint()
        
        print("✅ 指纹生成成功!")
        print(f"User-Agent: {fingerprint['user_agent']}")
        print(f"屏幕分辨率: {fingerprint['screen']['width']}x{fingerprint['screen']['height']}")
        print(f"时区: {fingerprint['timezone']}")
        print(f"语言: {fingerprint['language']}")
        
        return True
    except Exception as e:
        print(f"❌ 指纹生成失败: {e}")
        return False

def test_browser_config():
    """测试浏览器配置管理"""
    print("\n测试浏览器配置管理...")
    try:
        from browser_config import BrowserConfigManager
        from fingerprint_generator import FingerprintGenerator
        
        generator = FingerprintGenerator()
        fingerprint = generator.generate_fingerprint()
        
        config_manager = BrowserConfigManager()
        args = config_manager.get_chrome_launch_args("test_profile", fingerprint)
        
        print("✅ 配置管理测试成功!")
        print(f"生成了 {len(args)} 个Chrome启动参数")
        
        return True
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

def test_browser_launcher():
    """测试浏览器启动器（不实际启动浏览器）"""
    print("\n测试浏览器启动器...")
    try:
        from browser_launcher import BrowserLauncher
        
        launcher = BrowserLauncher()
        chrome_path = launcher.find_chrome_executable()
        
        if chrome_path:
            print(f"✅ 找到Chrome浏览器: {chrome_path}")
        else:
            print("⚠️  未找到Chrome浏览器，但启动器功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 启动器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 指纹浏览器功能测试")
    print("=" * 50)
    
    tests = [
        test_fingerprint_generator,
        test_browser_config,
        test_browser_launcher
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统可以正常使用")
        print("\n快速开始:")
        print("1. 运行 'python main.py -i' 启动交互模式")
        print("2. 或运行 'run.bat' (Windows) / './run.sh' (Linux/macOS)")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
