#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器 - 主程序入口
提供命令行界面和图形界面选项
"""

import argparse
import sys
import json
import time
from typing import Optional

from fingerprint_generator import FingerprintGenerator
from browser_config import BrowserConfigManager
from browser_launcher import BrowserLauncher


class FingerprintBrowserCLI:
    """指纹浏览器命令行界面"""
    
    def __init__(self):
        self.launcher = BrowserLauncher()
        self.config_manager = BrowserConfigManager()
        self.fingerprint_generator = FingerprintGenerator()
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("=" * 60)
        print("🌐 指纹浏览器 - 交互模式")
        print("=" * 60)
        
        while True:
            print("\n请选择操作:")
            print("1. 启动单个浏览器")
            print("2. 启动多个浏览器")
            print("3. 查看运行中的浏览器")
            print("4. 终止浏览器")
            print("5. 生成指纹预览")
            print("6. 管理配置文件")
            print("0. 退出")
            
            choice = input("\n请输入选项 (0-6): ").strip()
            
            try:
                if choice == "1":
                    self._launch_single_browser()
                elif choice == "2":
                    self._launch_multiple_browsers()
                elif choice == "3":
                    self._show_running_browsers()
                elif choice == "4":
                    self._kill_browser()
                elif choice == "5":
                    self._preview_fingerprint()
                elif choice == "6":
                    self._manage_profiles()
                elif choice == "0":
                    print("\n正在退出...")
                    self.launcher.cleanup_all()
                    break
                else:
                    print("❌ 无效选项，请重新选择")
            
            except KeyboardInterrupt:
                print("\n\n正在退出...")
                self.launcher.cleanup_all()
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")
    
    def _launch_single_browser(self):
        """启动单个浏览器"""
        print("\n🚀 启动单个浏览器")
        
        url = input("请输入要访问的URL (默认: https://www.google.com): ").strip()
        if not url:
            url = "https://www.google.com"
        
        temp_profile = input("使用临时配置? (y/n, 默认: y): ").strip().lower()
        temp_profile = temp_profile != 'n'
        
        profile_name = None
        if not temp_profile:
            profile_name = input("请输入配置文件名称: ").strip()
            if not profile_name:
                print("❌ 配置文件名称不能为空")
                return
        
        try:
            process, profile_dir = self.launcher.launch_browser(
                url=url, 
                temp_profile=temp_profile,
                profile_name=profile_name
            )
            print(f"✅ 浏览器启动成功! PID: {process.pid}")
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    def _launch_multiple_browsers(self):
        """启动多个浏览器"""
        print("\n🚀 启动多个浏览器")
        
        try:
            count = int(input("请输入要启动的浏览器数量: ").strip())
            if count <= 0 or count > 10:
                print("❌ 数量必须在1-10之间")
                return
        except ValueError:
            print("❌ 请输入有效数字")
            return
        
        url = input("请输入要访问的URL (默认: https://www.google.com): ").strip()
        if not url:
            url = "https://www.google.com"
        
        try:
            browsers = self.launcher.launch_multiple_browsers(count, url)
            print(f"✅ 成功启动 {len(browsers)} 个浏览器实例")
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    def _show_running_browsers(self):
        """显示运行中的浏览器"""
        print("\n📊 运行中的浏览器")
        
        browsers = self.launcher.get_running_browsers()
        
        if not browsers:
            print("当前没有运行中的浏览器")
            return
        
        print(f"\n共有 {len(browsers)} 个浏览器实例正在运行:")
        print("-" * 80)
        print(f"{'PID':<8} {'内存%':<8} {'CPU%':<8} {'配置类型':<10} {'User-Agent':<50}")
        print("-" * 80)
        
        for browser in browsers:
            ua = "N/A"
            if browser['fingerprint'] and 'user_agent' in browser['fingerprint']:
                ua = browser['fingerprint']['user_agent'][:47] + "..."
            
            config_type = "临时" if browser['temp_profile'] else "永久"
            
            print(f"{browser['pid']:<8} {browser['memory_percent']:<8.1f} "
                  f"{browser['cpu_percent']:<8.1f} {config_type:<10} {ua:<50}")
    
    def _kill_browser(self):
        """终止浏览器"""
        print("\n🛑 终止浏览器")
        
        browsers = self.launcher.get_running_browsers()
        if not browsers:
            print("当前没有运行中的浏览器")
            return
        
        print("运行中的浏览器:")
        for i, browser in enumerate(browsers, 1):
            print(f"{i}. PID: {browser['pid']}")
        
        print(f"{len(browsers) + 1}. 终止所有浏览器")
        
        try:
            choice = int(input("请选择要终止的浏览器: ").strip())
            
            if choice == len(browsers) + 1:
                self.launcher.kill_all_browsers()
                print("✅ 已终止所有浏览器")
            elif 1 <= choice <= len(browsers):
                pid = browsers[choice - 1]['pid']
                if self.launcher.kill_browser(pid):
                    print(f"✅ 已终止浏览器 PID: {pid}")
                else:
                    print(f"❌ 终止浏览器失败 PID: {pid}")
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _preview_fingerprint(self):
        """预览指纹"""
        print("\n🔍 生成指纹预览")
        
        fingerprint = self.fingerprint_generator.generate_fingerprint()
        
        print("\n生成的浏览器指纹:")
        print("-" * 50)
        print(f"User-Agent: {fingerprint['user_agent']}")
        print(f"屏幕分辨率: {fingerprint['screen']['width']}x{fingerprint['screen']['height']}")
        print(f"颜色深度: {fingerprint['screen']['color_depth']} bit")
        print(f"像素比: {fingerprint['screen']['pixel_ratio']}")
        print(f"时区: {fingerprint['timezone']}")
        print(f"语言: {fingerprint['language']}")
        print(f"硬件并发: {fingerprint['hardware']['concurrency']} 核")
        print(f"设备内存: {fingerprint['hardware']['device_memory']} GB")
        print(f"平台: {fingerprint['navigator']['platform']}")
        print(f"WebGL厂商: {fingerprint['webgl']['vendor']}")
        
        save = input("\n是否保存此指纹? (y/n): ").strip().lower()
        if save == 'y':
            filename = input("请输入文件名 (默认: fingerprint.json): ").strip()
            if not filename:
                filename = "fingerprint.json"
            
            self.fingerprint_generator.save_fingerprint(fingerprint, filename)
            print(f"✅ 指纹已保存到 {filename}")
    
    def _manage_profiles(self):
        """管理配置文件"""
        print("\n📁 配置文件管理")
        
        profiles = self.config_manager.list_profiles()
        
        if not profiles:
            print("当前没有保存的配置文件")
            return
        
        print(f"\n共有 {len(profiles)} 个配置文件:")
        for i, profile in enumerate(profiles, 1):
            print(f"{i}. {profile}")
        
        print(f"{len(profiles) + 1}. 删除配置文件")
        print(f"{len(profiles) + 2}. 查看配置文件详情")
        
        try:
            choice = int(input("请选择操作: ").strip())
            
            if choice == len(profiles) + 1:
                self._delete_profile(profiles)
            elif choice == len(profiles) + 2:
                self._show_profile_details(profiles)
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _delete_profile(self, profiles):
        """删除配置文件"""
        if not profiles:
            return
        
        print("\n选择要删除的配置文件:")
        for i, profile in enumerate(profiles, 1):
            print(f"{i}. {profile}")
        
        try:
            choice = int(input("请选择: ").strip())
            if 1 <= choice <= len(profiles):
                profile_name = profiles[choice - 1]
                confirm = input(f"确认删除配置文件 '{profile_name}'? (y/n): ").strip().lower()
                if confirm == 'y':
                    if self.config_manager.delete_profile(profile_name):
                        print(f"✅ 已删除配置文件 '{profile_name}'")
                    else:
                        print(f"❌ 删除配置文件失败")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _show_profile_details(self, profiles):
        """显示配置文件详情"""
        if not profiles:
            return
        
        print("\n选择要查看的配置文件:")
        for i, profile in enumerate(profiles, 1):
            print(f"{i}. {profile}")
        
        try:
            choice = int(input("请选择: ").strip())
            if 1 <= choice <= len(profiles):
                profile_name = profiles[choice - 1]
                fingerprint = self.config_manager.get_profile_fingerprint(profile_name)
                if fingerprint:
                    print(f"\n配置文件 '{profile_name}' 详情:")
                    print(json.dumps(fingerprint, indent=2, ensure_ascii=False))
                else:
                    print(f"❌ 无法读取配置文件详情")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="指纹浏览器 - 生成不同指纹的浏览器实例")
    parser.add_argument("--url", default="https://www.google.com", help="要访问的URL")
    parser.add_argument("--count", type=int, default=1, help="启动的浏览器数量")
    parser.add_argument("--interactive", "-i", action="store_true", help="启动交互模式")
    parser.add_argument("--profile", help="使用指定的配置文件名称")
    parser.add_argument("--temp", action="store_true", help="使用临时配置文件")
    
    args = parser.parse_args()
    
    if args.interactive:
        # 交互模式
        cli = FingerprintBrowserCLI()
        cli.run_interactive_mode()
    else:
        # 命令行模式
        launcher = BrowserLauncher()
        
        try:
            if args.count == 1:
                process, profile_dir = launcher.launch_browser(
                    url=args.url,
                    profile_name=args.profile,
                    temp_profile=args.temp or args.profile is None
                )
                print(f"浏览器启动成功! PID: {process.pid}")
                print("按 Ctrl+C 退出...")
                
                try:
                    process.wait()
                except KeyboardInterrupt:
                    print("\n正在退出...")
            else:
                browsers = launcher.launch_multiple_browsers(args.count, args.url)
                print(f"成功启动 {len(browsers)} 个浏览器实例")
                print("按 Ctrl+C 退出...")
                
                try:
                    while any(p.poll() is None for p, _ in browsers):
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n正在退出...")
        
        except Exception as e:
            print(f"错误: {e}")
            sys.exit(1)
        finally:
            launcher.cleanup_all()


if __name__ == "__main__":
    main()
