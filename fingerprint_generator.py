#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器 - 指纹生成器模块
生成随机的浏览器指纹信息，包括User-Agent、屏幕分辨率、时区、语言等
"""

import random
import json
from typing import Dict, List, Tuple
from datetime import datetime


class FingerprintGenerator:
    """浏览器指纹生成器"""
    
    def __init__(self):
        self.user_agents = self._load_user_agents()
        self.screen_resolutions = self._load_screen_resolutions()
        self.timezones = self._load_timezones()
        self.languages = self._load_languages()
        self.webgl_vendors = self._load_webgl_vendors()
        self.canvas_fingerprints = self._load_canvas_fingerprints()
    
    def _load_user_agents(self) -> List[str]:
        """加载常见的User-Agent列表"""
        return [
            # Chrome Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            
            # Chrome macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            
            # Chrome Linux
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            
            # Firefox Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            
            # Firefox macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
            
            # Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            
            # Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        ]
    
    def _load_screen_resolutions(self) -> List[Tuple[int, int]]:
        """加载常见的屏幕分辨率"""
        return [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
            (1280, 720), (1600, 900), (1024, 768), (1280, 800),
            (1680, 1050), (1920, 1200), (2560, 1440), (3840, 2160),
            (1280, 1024), (1152, 864), (1360, 768), (1600, 1200)
        ]
    
    def _load_timezones(self) -> List[str]:
        """加载常见的时区"""
        return [
            "Asia/Shanghai", "Asia/Tokyo", "Asia/Seoul", "Asia/Hong_Kong",
            "America/New_York", "America/Los_Angeles", "America/Chicago",
            "Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Moscow",
            "Australia/Sydney", "Asia/Singapore", "Asia/Dubai",
            "America/Toronto", "America/Sao_Paulo", "Asia/Kolkata"
        ]
    
    def _load_languages(self) -> List[str]:
        """加载常见的语言设置"""
        return [
            "zh-CN,zh;q=0.9,en;q=0.8",
            "en-US,en;q=0.9",
            "ja-JP,ja;q=0.9,en;q=0.8",
            "ko-KR,ko;q=0.9,en;q=0.8",
            "zh-TW,zh;q=0.9,en;q=0.8",
            "de-DE,de;q=0.9,en;q=0.8",
            "fr-FR,fr;q=0.9,en;q=0.8",
            "es-ES,es;q=0.9,en;q=0.8",
            "ru-RU,ru;q=0.9,en;q=0.8",
            "pt-BR,pt;q=0.9,en;q=0.8"
        ]
    
    def _load_webgl_vendors(self) -> List[Dict[str, str]]:
        """加载WebGL厂商信息"""
        return [
            {"vendor": "Google Inc. (NVIDIA)", "renderer": "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)"},
            {"vendor": "Google Inc. (Intel)", "renderer": "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)"},
            {"vendor": "Google Inc. (AMD)", "renderer": "ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)"},
            {"vendor": "Mozilla", "renderer": "Mozilla -- GeForce GTX 1060 6GB/PCIe/SSE2"},
            {"vendor": "WebKit", "renderer": "WebKit WebGL"},
        ]
    
    def _load_canvas_fingerprints(self) -> List[str]:
        """加载Canvas指纹模板"""
        return [
            "canvas_fp_1", "canvas_fp_2", "canvas_fp_3", "canvas_fp_4", "canvas_fp_5"
        ]
    
    def generate_fingerprint(self) -> Dict:
        """生成完整的浏览器指纹"""
        user_agent = random.choice(self.user_agents)
        screen_width, screen_height = random.choice(self.screen_resolutions)
        timezone = random.choice(self.timezones)
        language = random.choice(self.languages)
        webgl_info = random.choice(self.webgl_vendors)
        canvas_fp = random.choice(self.canvas_fingerprints)
        
        # 生成其他随机参数
        color_depth = random.choice([24, 32])
        pixel_ratio = random.choice([1, 1.25, 1.5, 2])
        hardware_concurrency = random.choice([2, 4, 6, 8, 12, 16])
        device_memory = random.choice([2, 4, 8, 16, 32])
        
        fingerprint = {
            "user_agent": user_agent,
            "screen": {
                "width": screen_width,
                "height": screen_height,
                "color_depth": color_depth,
                "pixel_ratio": pixel_ratio
            },
            "timezone": timezone,
            "language": language,
            "webgl": webgl_info,
            "canvas_fingerprint": canvas_fp,
            "hardware": {
                "concurrency": hardware_concurrency,
                "device_memory": device_memory
            },
            "navigator": {
                "platform": self._get_platform_from_ua(user_agent),
                "do_not_track": random.choice([None, "1"]),
                "cookie_enabled": True,
                "java_enabled": random.choice([True, False])
            },
            "generated_at": datetime.now().isoformat()
        }
        
        return fingerprint
    
    def _get_platform_from_ua(self, user_agent: str) -> str:
        """从User-Agent中提取平台信息"""
        if "Windows" in user_agent:
            return "Win32"
        elif "Macintosh" in user_agent:
            return "MacIntel"
        elif "Linux" in user_agent:
            return "Linux x86_64"
        else:
            return "Win32"  # 默认值
    
    def save_fingerprint(self, fingerprint: Dict, filepath: str):
        """保存指纹到文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(fingerprint, f, indent=2, ensure_ascii=False)
    
    def load_fingerprint(self, filepath: str) -> Dict:
        """从文件加载指纹"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)


if __name__ == "__main__":
    # 测试指纹生成器
    generator = FingerprintGenerator()
    fingerprint = generator.generate_fingerprint()
    print("生成的浏览器指纹:")
    print(json.dumps(fingerprint, indent=2, ensure_ascii=False))
